import { But<PERSON> } from "@/components/ui/button";
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from "@/components/ui/navigation-menu";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Menu, X, Globe } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  const services = [{
    title: "Web & Digital",
    description: "Fast-loading websites & web apps",
    href: "/services/web-digital",
    icon: "🌐"
  },{
    title: "Internal Software & Automation",
    description: "Custom tools & workflow automation",
    href: "/services/internal-software",
    icon: "⚡"
  }, {
    title: "Cloud Infrastructure",
    description: "Scalable storage & reliable hosting",
    href: "/services/cloud-infrastructure",
    icon: "☁️"
  }, {
    title: "Email & Communication",
    description: "Reliable email delivery systems",
    href: "/services/email-communication",
    icon: "📧"
  }, {
    title: "Security & Compliance",
    description: "Protection & rapid threat response",
    href: "/services/security-compliance",
    icon: "🔒"
  }, {
    title: "Tech Consulting",
    description: "Strategic technology planning",
    href: "/services/tech-consulting",
    icon: "🎯"
  }, {
    title: "Support & Maintenance",
    description: "Expert help with quick response",
    href: "/services/support-maintenance",
    icon: "🛠️"
  }];
  
  const navItems = [{
    label: "Ecosystem",
    href: "/products"
  }, {
    label: "Cases",
    href: "/cases"
  }, {
    label: "Team",
    href: "/team"
  }, {
    label: "Contact",
    href: "/contact"
  }];
  
  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'nl', name: 'Nederlands', flag: '🇳🇱' },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-xl border-b border-lumen-yellow/20">
      <div className="max-w-content mx-auto px-6 py-[12px]">
        <div className="flex items-center justify-between h-14">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 flex-shrink-0">
            <div className="w-7 h-7 bg-gradient-to-br from-lumen-yellow to-lumen-yellow-hover rounded-lg flex items-center justify-center">
              <div className="w-3 h-3 bg-lumen-charcoal rounded-sm"></div>
            </div>
            <span className="text-lg font-bold text-lumen-charcoal whitespace-nowrap">Lumiq</span>
          </Link>

          {/* Desktop Navigation - Centered */}
          <div className="hidden lg:flex items-center justify-center flex-1">
            <NavigationMenu>
              <NavigationMenuList className="space-x-6">
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="bg-transparent hover:bg-lumen-yellow/10 text-lumen-charcoal font-medium text-sm px-3 py-2 h-auto whitespace-nowrap">
                    Services
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid grid-cols-2 gap-4 p-6 w-[600px] bg-white/95 backdrop-blur-sm">
                      {services.map(service => (
                        <NavigationMenuLink key={service.href} asChild>
                          <Link to={service.href} className="group block p-4 rounded-lg hover:bg-lumen-yellow/10 transition-colors">
                            <div className="flex items-start space-x-3">
                              <span className="text-2xl">{service.icon}</span>
                              <div>
                                <h3 className="font-semibold text-lumen-charcoal group-hover:text-lumen-yellow-hover transition-colors">
                                  {service.title}
                                </h3>
                                <p className="text-sm text-lumen-mid-gray mt-1">
                                  {service.description}
                                </p>
                              </div>
                            </div>
                          </Link>
                        </NavigationMenuLink>
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                {navItems.map(item => (
                  <NavigationMenuItem key={item.label}>
                    <Link to={item.href} className="text-lumen-charcoal hover:text-lumen-yellow-hover transition-colors font-medium px-3 py-2 rounded-md hover:bg-lumen-yellow/10 text-sm whitespace-nowrap">
                      {item.label}
                    </Link>
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Language Dropdown and CTA Button */}
          <div className="hidden lg:flex items-center space-x-3 flex-shrink-0">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="border-lumen-yellow/30 hover:bg-lumen-yellow/10">
                  <Globe size={16} className="mr-2" />
                  EN
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48 bg-white/95 backdrop-blur-sm border border-lumen-yellow/20">
                {languages.map((language) => (
                  <DropdownMenuItem key={language.code} className="cursor-pointer hover:bg-lumen-yellow/10">
                    <span className="mr-2">{language.flag}</span>
                    {language.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
            
            <Button size="sm" className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold whitespace-nowrap">
              Let's talk
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="lg:hidden p-2 text-lumen-charcoal">
            {isMenuOpen ? <X size={20} /> : <Menu size={20} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-lumen-yellow/20 bg-white/95 backdrop-blur-xl">
            <nav className="py-4 space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold text-lumen-charcoal px-4">Services</h3>
                {services.map(service => (
                  <Link
                    key={service.href}
                    to={service.href}
                    className="block px-6 py-2 text-lumen-charcoal hover:text-lumen-yellow-hover transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="mr-2">{service.icon}</span>
                    {service.title}
                  </Link>
                ))}
              </div>
              {navItems.map(item => (
                <Link
                  key={item.label}
                  to={item.href}
                  className="block px-4 py-2 text-lumen-charcoal hover:text-lumen-yellow-hover transition-colors font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              
              {/* Mobile Language Selection */}
              <div className="px-4 py-2">
                <h3 className="font-semibold text-lumen-charcoal mb-2">Language</h3>
                <div className="space-y-1">
                  {languages.map((language) => (
                    <button
                      key={language.code}
                      className="flex items-center w-full px-2 py-1 text-lumen-charcoal hover:text-lumen-yellow-hover transition-colors"
                    >
                      <span className="mr-2">{language.flag}</span>
                      {language.name}
                    </button>
                  ))}
                </div>
              </div>
              
              <Button className="w-full mx-4 bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold mt-4">
                Let's talk
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
