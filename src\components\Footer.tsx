import { Link } from "react-router-dom";
const Footer = () => {
  const navItems = [{
    label: "Ecosystem",
    href: "/products"
  }, {
    label: "Cases",
    href: "/cases"
  }, {
    label: "Our Team",
    href: "/team"
  }, {
    label: "Contact",
    href: "/contact"
  }];
  const services = [{
    title: "Web & Digital",
    href: "/services/web-digital"
  }, {
    title: "Cloud Infrastructure",
    href: "/services/cloud-infrastructure"
  }, {
    title: "Email & Communication",
    href: "/services/email-communication"
  }, {
    title: "Internal Software & Automation",
    href: "/services/internal-software"
  }, {
    title: "Security & Compliance",
    href: "/services/security-compliance"
  }, {
    title: "Tech Consulting",
    href: "/services/tech-consulting"
  }, {
    title: "Support & Maintenance",
    href: "/services/support-maintenance"
  }];
  return <footer className="w-full bg-lumen-off-white border-t border-lumen-yellow/20 py-12">
      <div className="max-w-content mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Logo and company info */}
          <div className="md:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-6 h-6 bg-gradient-to-br from-lumen-yellow to-lumen-yellow-hover rounded-lg flex items-center justify-center">
                <div className="w-3 h-3 bg-lumen-charcoal rounded-sm"></div>
              </div>
              <span className="text-lg font-bold text-lumen-charcoal">Lumiq</span>
            </div>
            <p className="text-sm text-lumen-mid-gray leading-relaxed">
              Systems built to last.<br></br> Engineered for tomorrow.
            </p>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-bold text-lumen-charcoal mb-4">Services</h4>
            <ul className="space-y-2 text-sm text-lumen-mid-gray">
              {services.map(service => <li key={service.href}>
                  <Link to={service.href} className="hover:text-lumen-charcoal transition-colors">
                    {service.title}
                  </Link>
                </li>)}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="font-bold text-lumen-charcoal mb-4">Company</h4>
            <ul className="space-y-2 text-sm text-lumen-mid-gray">
              {navItems.map(item => <li key={item.label}>
                  <Link to={item.href} className="hover:text-lumen-charcoal transition-colors">
                    {item.label}
                  </Link>
                </li>)}
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="font-bold text-lumen-charcoal mb-4">Let's talk</h4>
            <div className="space-y-3">
              <p className="text-sm text-lumen-mid-gray">
                Ready for reliable tech?
              </p>
              <a href="#" className="inline-block bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-4 py-2 rounded-lg text-sm transition-colors">
                Book Health Check
              </a>
            </div>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="border-t border-lumen-yellow/20 mt-8 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-xs text-lumen-mid-gray">© 2025 Lumiq. All rights reserved.</p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link to="/terms" className="text-xs text-lumen-mid-gray hover:text-lumen-charcoal transition-colors">Terms</Link>
            <Link to="/privacy" className="text-xs text-lumen-mid-gray hover:text-lumen-charcoal transition-colors">Privacy</Link>
            <Link to="/cookies" className="text-xs text-lumen-mid-gray hover:text-lumen-charcoal transition-colors">Cookies</Link>
          </div>
        </div>
      </div>
    </footer>;
};
export default Footer;