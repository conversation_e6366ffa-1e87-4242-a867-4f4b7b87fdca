
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Globe, Zap, Shield, Search, Smartphone, BarChart3, Settings, ExternalLink } from "lucide-react";
import { useState, useEffect } from "react";

const WebDigital = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('web-digital-hero');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };
    const heroSection = document.getElementById('web-digital-hero');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  const portfolioItems = [
    {
      title: "Artisan Coffee Co.",
      category: "E-commerce",
      description: "Modern coffee shop with online ordering and subscription service",
      url: "https://example.com/coffee",
      image: "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["2.3s → 0.8s load time", "180% increase in online orders"]
    },
    {
      title: "Rodriguez Legal",
      category: "Professional Services",
      description: "Clean, professional law firm website with client portal",
      url: "https://example.com/legal",
      image: "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["Mobile-first design", "40% more consultations"]
    },
    {
      title: "Coastal Manufacturing",
      category: "Industrial",
      description: "B2B manufacturing site with product catalog and quote system",
      url: "https://example.com/manufacturing",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["Custom quote system", "60% faster inquiries"]
    }
  ];

  const services = [
    {
      icon: Globe,
      title: "Website Development",
      commitment: "pages < 1s, 99.9% uptime",
      description: "Fast-loading websites that stay online when your customers need them most."
    },
    {
      icon: Smartphone,
      title: "Mobile Optimization",
      commitment: "perfect on all devices",
      description: "Responsive design that works flawlessly on phones, tablets, and desktops."
    },
    {
      icon: Search,
      title: "SEO & Performance",
      commitment: "search-ready from day one",
      description: "Built-in optimization to help your business get found online."
    },
    {
      icon: Shield,
      title: "Secure Hosting",
      commitment: "enterprise-grade security",
      description: "Protected hosting with automatic backups and security monitoring."
    },
    {
      icon: BarChart3,
      title: "Analytics & Insights",
      commitment: "data-driven decisions",
      description: "Track performance and understand your visitors with integrated analytics."
    },
    {
      icon: Settings,
      title: "Ongoing Support",
      commitment: "30-min response time",
      description: "Expert maintenance and support to keep your website running smoothly."
    }
  ];

  const capabilities = [
    "Strategic planning and user experience design",
    "Custom website development and programming",
    "Mobile-responsive design for all devices",
    "Search engine optimization (SEO)",
    "Secure hosting and infrastructure management",
    "Performance optimization and monitoring",
    "Content management system integration",
    "E-commerce and payment processing",
    "Third-party integrations and APIs",
    "Ongoing maintenance and support"
  ];

  const testimonials = [
    {
      quote: "Lumiq transformed our online presence completely. Our website now loads instantly and we've seen a 200% increase in online inquiries.",
      author: "Sarah Chen",
      company: "Artisan Coffee Co.",
      size: "25 employees"
    },
    {
      quote: "They handled everything from planning to launch. Professional, reliable, and delivered exactly what they promised.",
      author: "Mark Rodriguez",
      company: "Rodriguez Legal",
      size: "12 employees"
    },
    {
      quote: "Finally, a website that actually works on mobile. Our customers can now easily browse and purchase from any device.",
      author: "Lisa Thompson",
      company: "Coastal Manufacturing",
      size: "85 employees"
    }
  ];

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Background gradient clouds - matching homepage */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-lumen-yellow/10 to-white/50 rounded-full blur-3xl transform -translate-y-1/2"></div>
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-gradient-to-bl from-lumen-yellow/15 to-lumen-yellow/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-gradient-to-tr from-lumen-yellow/8 to-white/30 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10">
        <Header />

        {/* Hero Section - matching homepage style */}
        <section
          id="web-digital-hero"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          className="w-full bg-transparent py-20 relative overflow-hidden lg:py-[90px] pt-28"
        >
          {/* Mouse-following gradient cloud */}
          {isHovering && (
            <div
              className="absolute w-96 h-96 bg-gradient-to-br from-lumen-yellow/20 to-lumen-yellow/5 rounded-full blur-3xl pointer-events-none transition-all duration-300 ease-out"
              style={{
                left: mousePosition.x - 192,
                top: mousePosition.y - 192
              }}
            />
          )}

          <div className="max-w-content mx-auto px-6 m-10">
            <div className="flex items-center justify-center min-h-[60vh]">
              <div className="space-y-10 relative z-10 text-center max-w-4xl">
                <div className="space-y-6">
                  <div className="inline-block px-4 py-2 bg-lumen-yellow/20 rounded-full border border-lumen-yellow/30 backdrop-blur-sm">
                    <span className="text-sm font-medium text-lumen-charcoal">🌐 Web & Digital Solutions</span>
                  </div>
                  <h1 className="text-5xl lg:text-6xl font-bold text-lumen-charcoal leading-tight xl:text-7xl">
                    Websites that work.<br />
                    <span className="bg-gradient-to-r from-lumen-charcoal to-lumen-mid-gray bg-clip-text text-transparent">
                      Built for business.
                    </span>
                  </h1>
                  <p className="text-xl lg:text-2xl text-lumen-mid-gray leading-relaxed max-w-3xl mx-auto">
                    Professional web development from planning to launch. Fast, secure, and designed to grow your business.
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-gradient-to-r from-lumen-yellow to-lumen-yellow-hover hover:from-lumen-yellow-hover hover:to-lumen-yellow text-lumen-charcoal font-bold px-10 py-6 text-lg rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
                    Book a consultation
                  </Button>
                  <Button variant="outline" size="lg" className="border-2 border-lumen-yellow/30 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg rounded-2xl bg-white/50 backdrop-blur-sm hover:bg-lumen-yellow/10 transition-all duration-300">
                    View our work
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Floating elements */}
          <div className="absolute top-1/4 right-1/4 w-8 h-8 bg-gradient-to-br from-lumen-yellow to-lumen-yellow-hover rounded-full animate-float shadow-lg"></div>
          <div className="absolute bottom-1/3 left-1/4 w-6 h-6 bg-gradient-to-br from-lumen-yellow/70 to-lumen-yellow-hover/70 rounded-full animate-float delay-500 shadow-md"></div>
          <div className="absolute top-1/2 left-1/6 w-4 h-4 bg-gradient-to-br from-lumen-yellow/50 to-lumen-yellow-hover/50 rounded-full animate-float delay-1000 shadow-sm"></div>
        </section>

        {/* Services Grid Section - matching homepage style */}
        <section className="w-full bg-gradient-to-br from-lumen-off-white via-white to-lumen-off-white py-20 lg:py-28 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-lumen-yellow/3 via-transparent to-lumen-yellow/5"></div>
          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
                Complete web solutions
              </h2>
              <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto leading-relaxed">
                From planning to launch, we handle every aspect of your web presence with measurable commitments you can count on.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="group bg-white/80 backdrop-blur-sm rounded-xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-lumen-yellow/40"
                >
                  <div className="text-3xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <service.icon className="h-8 w-8 text-lumen-yellow" />
                  </div>
                  <h3 className="text-xl font-bold text-lumen-charcoal mb-3 group-hover:text-lumen-yellow-hover transition-colors duration-300">
                    {service.title}
                  </h3>
                  <div className="text-sm font-mono text-lumen-charcoal mb-4 bg-gradient-to-r from-lumen-yellow/20 to-lumen-yellow/10 px-3 py-2 rounded-xl border border-lumen-yellow/30">
                    {service.commitment}
                  </div>
                  <p className="text-sm text-lumen-mid-gray leading-relaxed">
                    {service.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Capabilities Section */}
        <section className="w-full bg-white py-20 lg:py-28">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
                End-to-end capabilities
              </h2>
              <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto leading-relaxed">
                We handle everything from strategic planning to ongoing support, so you can focus on growing your business.
              </p>
            </div>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {capabilities.map((capability, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-1" />
                  <span className="text-lumen-charcoal text-lg">{capability}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Our Work Section */}
        <section className="w-full bg-gradient-to-br from-lumen-off-white via-white to-lumen-off-white py-16 lg:py-20 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-lumen-yellow/3 via-transparent to-lumen-yellow/5"></div>
          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal section-beam mb-6">
                Our work
              </h2>
              <p className="text-lg text-lumen-mid-gray max-w-2xl mx-auto leading-relaxed">
                Real websites we've built for businesses across different industries.
              </p>
            </div>

            {/* Grid Container */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {portfolioItems.map((item, index) => (
                <div
                  key={index}
                  className="group relative bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer"
                  onClick={() => window.open(item.url, '_blank')}
                >
                  {/* Website Screenshot - 16:9 aspect ratio for 1080p screens */}
                  <div className="relative aspect-[16/9] overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                    />

                    {/* Overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-t from-lumen-charcoal/90 via-lumen-charcoal/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <div className="flex items-center justify-between mb-3">
                          <span className="px-3 py-1 bg-lumen-yellow text-lumen-charcoal text-xs font-medium rounded-full">
                            {item.category}
                          </span>
                          <ExternalLink className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                        <p className="text-white/90 text-sm mb-3">{item.description}</p>
                        <div className="space-y-1">
                          {item.metrics.map((metric, metricIndex) => (
                            <div key={metricIndex} className="text-xs text-lumen-yellow font-medium">
                              ✓ {metric}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section - matching homepage style */}
        <section className="w-full bg-white py-16 lg:py-20">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal section-beam mb-6">
                What business owners say
              </h2>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-3 gap-8">
                {testimonials.map((testimonial, index) => (
                  <div key={index} className="text-center">
                    <blockquote className="text-xl lg:text-xl text-lumen-charcoal mb-6 leading-relaxed">
                      "{testimonial.quote}"
                    </blockquote>
                    <div className="space-y-1">
                      <div className="font-bold text-lumen-charcoal">
                        {testimonial.author}
                      </div>
                      <div className="text-lumen-mid-gray">
                        {testimonial.company} • {testimonial.size}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section - matching homepage style */}
        <section className="w-full bg-lumen-yellow py-16 lg:py-20">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-4">
                Ready to transform your web presence?
              </h2>
              <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto">
                Let's discuss how we can build a website that drives real business results. Professional consultation to understand your needs and goals.
              </p>
            </div>

            <div className="text-center">
              <Button
                size="lg"
                className="bg-lumen-charcoal hover:bg-lumen-charcoal/90 text-white font-bold py-6 px-12 text-lg rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
              >
                Book your consultation
              </Button>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default WebDigital;