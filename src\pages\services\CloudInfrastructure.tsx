import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Cloud, Shield, Zap, Globe, BarChart3, Server } from "lucide-react";
import { useState, useEffect } from "react";

const CloudInfrastructure = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('cloud-infrastructure-hero');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };
    const heroSection = document.getElementById('cloud-infrastructure-hero');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  const cloudExamples = [
    {
      title: "Retail Chain Backup",
      category: "Data Protection",
      description: "Automated daily backups for 15 store locations with instant recovery",
      image: "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["15-minute backups", "99.9% data recovery rate"]
    },
    {
      title: "Law Firm Hosting",
      category: "Secure Infrastructure",
      description: "Compliant cloud hosting with client portal and document storage",
      image: "https://images.unsplash.com/photo-*************-d10d557cf95f?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["Bank-level security", "24/7 uptime monitoring"]
    },
    {
      title: "Restaurant Group Storage",
      category: "Scalable Solutions",
      description: "Cloud storage that grows from 5 to 50 locations seamlessly",
      image: "https://images.unsplash.com/photo-*************-338989a2e8c0?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["Auto-scaling storage", "50% cost reduction"]
    }
  ];

  const services = [
    {
      icon: Cloud,
      title: "Cloud Storage",
      commitment: "unlimited growth, secure access",
      description: "Scalable storage that grows with your business, accessible from anywhere."
    },
    {
      icon: Server,
      title: "Managed Hosting",
      commitment: "99.9% uptime guarantee",
      description: "Reliable hosting with automatic updates and security monitoring."
    },
    {
      icon: Shield,
      title: "Data Backup",
      commitment: "15-minute automated backups",
      description: "Your data protected with frequent backups and instant recovery options."
    },
    {
      icon: Zap,
      title: "Performance Optimization",
      commitment: "lightning-fast load times",
      description: "Optimized infrastructure for maximum speed and reliability."
    },
    {
      icon: Globe,
      title: "Global Access",
      commitment: "worldwide availability",
      description: "Access your data and applications from anywhere in the world."
    },
    {
      icon: BarChart3,
      title: "Usage Monitoring",
      commitment: "real-time insights",
      description: "Track usage, performance, and costs with detailed reporting."
    }
  ];

  const capabilities = [
    "Cloud storage planning and migration strategy",
    "Secure hosting setup and configuration",
    "Automated backup systems and disaster recovery",
    "Performance monitoring and optimization",
    "Data security and compliance management",
    "Scalable infrastructure that grows with you",
    "24/7 technical support and monitoring",
    "Cost optimization and usage analytics",
    "Multi-location synchronization and access",
    "Regular security updates and maintenance"
  ];

  const testimonials = [
    {
      quote: "Lumiq moved our entire business to the cloud seamlessly. No downtime, and now we can access everything from anywhere.",
      author: "Jennifer Martinez",
      company: "Martinez Retail Group",
      size: "35 employees"
    },
    {
      quote: "Their backup system saved us when our office flooded. Everything was back online in hours, not days.",
      author: "David Kim",
      company: "Kim & Associates Law",
      size: "18 employees"
    },
    {
      quote: "Finally, cloud storage that makes sense for our budget. It grows as we grow, and we only pay for what we use.",
      author: "Maria Santos",
      company: "Santos Restaurant Group",
      size: "120 employees"
    }
  ];

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Background gradient clouds - matching homepage */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-lumen-yellow/10 to-white/50 rounded-full blur-3xl transform -translate-y-1/2"></div>
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-gradient-to-bl from-lumen-yellow/15 to-lumen-yellow/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-gradient-to-tr from-lumen-yellow/8 to-white/30 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10">
        <Header />

        {/* Hero Section - matching homepage style */}
        <section
          id="cloud-infrastructure-hero"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          className="w-full bg-transparent py-20 relative overflow-hidden lg:py-[90px] pt-28"
        >
          {/* Mouse-following gradient cloud */}
          {isHovering && (
            <div
              className="absolute w-96 h-96 bg-gradient-to-br from-lumen-yellow/20 to-lumen-yellow/5 rounded-full blur-3xl pointer-events-none transition-all duration-300 ease-out"
              style={{
                left: mousePosition.x - 192,
                top: mousePosition.y - 192
              }}
            />
          )}

          <div className="max-w-content mx-auto px-6 m-10">
            <div className="flex items-center justify-center min-h-[60vh]">
              <div className="space-y-10 relative z-10 text-center max-w-4xl">
                <div className="space-y-6">
                  <div className="inline-block px-4 py-2 bg-lumen-yellow/20 rounded-full border border-lumen-yellow/30 backdrop-blur-sm">
                    <span className="text-sm font-medium text-lumen-charcoal">☁️ Cloud Infrastructure</span>
                  </div>
                  <h1 className="text-5xl lg:text-6xl font-bold text-lumen-charcoal leading-tight xl:text-7xl">
                    Cloud that works.<br />
                    <span className="bg-gradient-to-r from-lumen-charcoal to-lumen-mid-gray bg-clip-text text-transparent">
                      Built for business.
                    </span>
                  </h1>
                  <p className="text-xl lg:text-2xl text-lumen-mid-gray leading-relaxed max-w-3xl mx-auto">
                    Reliable cloud storage and hosting that grows with your business. Secure, fast, and always accessible.
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-gradient-to-r from-lumen-yellow to-lumen-yellow-hover hover:from-lumen-yellow-hover hover:to-lumen-yellow text-lumen-charcoal font-bold px-10 py-6 text-lg rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
                    Book a consultation
                  </Button>
                  <Button variant="outline" size="lg" className="border-2 border-lumen-yellow/30 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg rounded-2xl hover:bg-lumen-yellow/10 transition-all duration-300">
                    Free infrastructure audit
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Services Grid Section - matching homepage style */}
        <section className="w-full bg-gradient-to-br from-lumen-off-white via-white to-lumen-off-white py-20 lg:py-28 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-lumen-yellow/3 via-transparent to-lumen-yellow/5"></div>
          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
                Complete cloud solutions
              </h2>
              <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto leading-relaxed">
                From storage to hosting, we handle every aspect of your cloud infrastructure with reliable commitments you can count on.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="group bg-white/80 backdrop-blur-sm rounded-xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-lumen-yellow/40"
                >
                  <div className="text-3xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <service.icon className="h-8 w-8 text-lumen-yellow" />
                  </div>
                  <h3 className="text-xl font-bold text-lumen-charcoal mb-3 group-hover:text-lumen-yellow-hover transition-colors duration-300">
                    {service.title}
                  </h3>
                  <div className="text-sm text-lumen-yellow font-medium mb-3 uppercase tracking-wide">
                    {service.commitment}
                  </div>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    {service.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Capabilities Section */}
        <section className="w-full bg-white py-20 lg:py-28">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
                End-to-end cloud management
              </h2>
              <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto leading-relaxed">
                We handle everything from planning to ongoing support, so you can focus on growing your business.
              </p>
            </div>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {capabilities.map((capability, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-1" />
                  <span className="text-lumen-charcoal text-lg">{capability}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Our Work Section */}
        <section className="w-full bg-gradient-to-br from-lumen-off-white via-white to-lumen-off-white py-16 lg:py-20 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-lumen-yellow/3 via-transparent to-lumen-yellow/5"></div>
          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal section-beam mb-6">
                Cloud solutions in action
              </h2>
              <p className="text-lg text-lumen-mid-gray max-w-2xl mx-auto leading-relaxed">
                Real cloud infrastructure we've built for businesses across different industries.
              </p>
            </div>

            {/* Grid Container */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {cloudExamples.map((item, index) => (
                <div
                  key={index}
                  className="group relative bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
                >
                  {/* Image - 16:9 aspect ratio for 1080p screens */}
                  <div className="relative aspect-[16/9] overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                    />

                    {/* Overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-t from-lumen-charcoal/90 via-lumen-charcoal/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <div className="flex items-center justify-between mb-3">
                          <span className="px-3 py-1 bg-lumen-yellow text-lumen-charcoal text-xs font-medium rounded-full">
                            {item.category}
                          </span>
                        </div>
                        <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                        <p className="text-white/90 text-sm mb-3">{item.description}</p>
                        <div className="space-y-1">
                          {item.metrics.map((metric, metricIndex) => (
                            <div key={metricIndex} className="text-xs text-lumen-yellow font-medium">
                              • {metric}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Card Content */}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="px-3 py-1 bg-lumen-yellow/20 text-lumen-charcoal text-xs font-medium rounded-full">
                        {item.category}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold text-lumen-charcoal mb-2 group-hover:text-lumen-yellow-hover transition-colors duration-300">
                      {item.title}
                    </h3>
                    <p className="text-lumen-mid-gray text-sm mb-4">{item.description}</p>
                    <div className="space-y-2">
                      {item.metrics.map((metric, metricIndex) => (
                        <div key={metricIndex} className="text-xs text-lumen-yellow font-medium">
                          • {metric}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="w-full bg-white py-16 lg:py-20">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal section-beam mb-6">
                What our clients say
              </h2>
              <p className="text-lg text-lumen-mid-gray max-w-2xl mx-auto leading-relaxed">
                Real results from businesses that trusted us with their cloud infrastructure.
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-3 gap-8">
                {testimonials.map((testimonial, index) => (
                  <div key={index} className="text-center">
                    <blockquote className="text-xl lg:text-xl text-lumen-charcoal mb-6 leading-relaxed">
                      "{testimonial.quote}"
                    </blockquote>
                    <div className="space-y-1">
                      <div className="font-bold text-lumen-charcoal">
                        {testimonial.author}
                      </div>
                      <div className="text-lumen-mid-gray">
                        {testimonial.company} • {testimonial.size}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section - matching homepage style */}
        <section className="w-full bg-lumen-yellow py-16 lg:py-20">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-4">
                Ready to move to the cloud?
              </h2>
              <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto">
                Let's discuss how we can build cloud infrastructure that supports your business growth. Professional consultation to understand your needs and goals.
              </p>
            </div>

            <div className="text-center">
              <Button
                size="lg"
                className="bg-lumen-charcoal hover:bg-lumen-charcoal/90 text-white font-bold py-6 px-12 text-lg rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
              >
                Book your consultation
              </Button>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default CloudInfrastructure;
