
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

const Contact = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('contact-hero-section');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const heroSection = document.getElementById('contact-hero-section');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-16 lg:pt-20">
          <section
            id="contact-hero-section"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            className="w-full bg-gradient-to-br from-lumen-yellow via-lumen-yellow/95 to-lumen-yellow-hover py-20 lg:py-28 pt-28 relative overflow-hidden"
          >
            {/* Mouse-following gradient cloud */}
            {isHovering && (
              <div
                className="absolute w-96 h-96 bg-gradient-to-br from-white/30 to-lumen-yellow-hover/20 rounded-full blur-3xl pointer-events-none transition-all duration-500 ease-out"
                style={{
                  left: mousePosition.x - 192,
                  top: mousePosition.y - 192
                }}
              />
            )}

            
            {/* Background pattern overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-lumen-yellow/10 to-lumen-yellow-hover/20"></div>

            <div className="max-w-content mx-auto px-6 relative z-10">
              <div className="text-center mb-8">
                <div className="inline-block px-4 py-2 bg-white/20 rounded-full border border-white/30 backdrop-blur-sm mb-6">
                  <span className="text-sm font-medium text-lumen-charcoal">💬 Let's build something amazing together</span>
                </div>
                <h2 className="text-4xl lg:text-5xl font-bold leading-tight xl:text-6xl mb-6">
                  <span className="text-lumen-charcoal">Let's grab a coffee! </span>
               
                </h2>
                <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto leading-relaxed">
                  Ready to transform your technology? Let's start the conversation and discover how we can help your business thrive.
                </p>
              </div>
            </div>
          </section>
          <div className="py-20 lg:py-28 pt-20">
        <div className="max-w-content mx-auto px-6">


          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div className="bg-white rounded-xl p-8 border border-lumen-yellow/20 shadow-lg">
              <h2 className="text-2xl font-bold text-lumen-charcoal mb-6">Send us a message</h2>
              <form className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="First Name"
                    className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow"
                  />
                  <input
                    type="text"
                    placeholder="Last Name"
                    className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow"
                  />
                </div>
                <input
                  type="email"
                  placeholder="Email Address"
                  className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow"
                />
                <input
                  type="text"
                  placeholder="Company"
                  className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow"
                />
                <textarea
                  placeholder="Tell us about your project..."
                  rows={6}
                  className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow resize-none"
                ></textarea>
                <Button className="w-full bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold py-3">
                  Send Message
                </Button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <div className="bg-gradient-to-br from-lumen-off-white to-lumen-yellow/10 rounded-xl p-8">
                <h3 className="text-xl font-bold text-lumen-charcoal mb-4">Let's talk</h3>
                <p className="text-lumen-mid-gray mb-6">
                  Ready to discuss your project? Book a free consultation and discover how we can help.
                </p>
                <Button className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold">
                  Book Free Consultation
                </Button>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-lumen-charcoal mb-2">Email</h4>
                  <p className="text-lumen-mid-gray"><EMAIL></p>
                </div>
                <div>
                  <h4 className="font-semibold text-lumen-charcoal mb-2">Phone</h4>
                  <p className="text-lumen-mid-gray">+****************</p>
                </div>
                <div>
                  <h4 className="font-semibold text-lumen-charcoal mb-2">Office</h4>
                  <p className="text-lumen-mid-gray">
                    123 Tech Street<br />
                    San Francisco, CA 94105
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-lumen-charcoal mb-2">Response Time</h4>
                  <p className="text-lumen-mid-gray">We typically respond within 2 hours during business hours</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </main>

      <Footer />
    </div>
  );
};

export default Contact;
