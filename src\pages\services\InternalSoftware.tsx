
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Cog, BarChart3, Database, Workflow, Bot, Settings } from "lucide-react";
import { useState, useEffect } from "react";

const InternalSoftware = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('internal-software-hero');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };
    const heroSection = document.getElementById('internal-software-hero');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  const automationExamples = [
    {
      title: "Inventory Management System",
      category: "Process Automation",
      description: "Automated inventory tracking and reordering for 12-location retail chain",
      image: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["85% less manual counting", "40% reduction in stockouts", "6-month ROI"]
    },
    {
      title: "Client Onboarding Portal",
      category: "Workflow Automation",
      description: "Streamlined client intake and document collection for professional services",
      image: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["3 hours → 20 minutes per client", "95% faster document collection", "300% ROI in year 1"]
    },
    {
      title: "Production Scheduling Tool",
      category: "Custom Software",
      description: "Smart scheduling system that optimizes production capacity and reduces waste",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=1920&h=1080&fit=crop&crop=center",
      metrics: ["25% increase in throughput", "60% reduction in scheduling time", "8-month payback"]
    }
  ];

  const services = [
    {
      icon: Bot,
      title: "Process Automation",
      commitment: "≥50% time savings guaranteed",
      description: "Eliminate repetitive tasks with smart automation that works 24/7."
    },
    {
      icon: Database,
      title: "Data Integration",
      commitment: "single source of truth",
      description: "Connect all your systems for seamless data flow and real-time insights."
    },
    {
      icon: Workflow,
      title: "Workflow Optimization",
      commitment: "30% efficiency boost minimum",
      description: "Streamline complex processes with custom workflow management tools."
    },
    {
      icon: BarChart3,
      title: "Business Intelligence",
      commitment: "actionable insights daily",
      description: "Transform your data into powerful dashboards and automated reports."
    },
    {
      icon: Cog,
      title: "System Integration",
      commitment: "seamless connectivity",
      description: "Make your existing software work together like a unified system."
    },
    {
      icon: Settings,
      title: "Custom Applications",
      commitment: "built for your exact needs",
      description: "Tailored software solutions that fit your unique business requirements."
    }
  ];

  const capabilities = [
    "Business process analysis and optimization strategy",
    "Custom software development and programming",
    "Database design and integration architecture",
    "Workflow automation and process digitization",
    "API development and third-party integrations",
    "Business intelligence and reporting dashboards",
    "Mobile and web application development",
    "Legacy system modernization and migration",
    "User training and comprehensive documentation",
    "Ongoing support and feature enhancement"
  ];

  const testimonials = [
    {
      quote: "Lumiq automated our entire inventory process. We went from 20 hours of manual work per week to just 2 hours. The ROI was incredible.",
      author: "Michael Torres",
      company: "Torres Retail Group",
      size: "45 employees"
    },
    {
      quote: "Their client onboarding system cut our processing time from 3 hours to 20 minutes per client. We can now handle 5x more clients with the same team.",
      author: "Rachel Kim",
      company: "Kim Professional Services",
      size: "28 employees"
    },
    {
      quote: "The production scheduling tool they built increased our throughput by 25% and eliminated scheduling conflicts completely. Best investment we've made.",
      author: "James Rodriguez",
      company: "Rodriguez Manufacturing",
      size: "95 employees"
    }
  ];

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Background gradient clouds - matching homepage */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-lumen-yellow/10 to-white/50 rounded-full blur-3xl transform -translate-y-1/2"></div>
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-gradient-to-bl from-lumen-yellow/15 to-lumen-yellow/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-gradient-to-tr from-lumen-yellow/8 to-white/30 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10">
        <Header />

        {/* Hero Section - matching homepage style */}
        <section
          id="internal-software-hero"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          className="w-full bg-transparent py-20 relative overflow-hidden lg:py-[90px] pt-28"
        >
          {/* Mouse-following gradient cloud */}
          {isHovering && (
            <div
              className="absolute w-96 h-96 bg-gradient-to-br from-lumen-yellow/20 to-lumen-yellow/5 rounded-full blur-3xl pointer-events-none transition-all duration-300 ease-out"
              style={{
                left: mousePosition.x - 192,
                top: mousePosition.y - 192
              }}
            />
          )}

          <div className="max-w-content mx-auto px-6 m-10">
            <div className="flex items-center justify-center min-h-[60vh]">
              <div className="space-y-10 relative z-10 text-center max-w-4xl">
                <div className="space-y-6">
                  <div className="inline-block px-4 py-2 bg-lumen-yellow/20 rounded-full border border-lumen-yellow/30 backdrop-blur-sm">
                    <span className="text-sm font-medium text-lumen-charcoal">⚡ Custom Software & Automation</span>
                  </div>
                  <h1 className="text-5xl lg:text-6xl font-bold text-lumen-charcoal leading-tight xl:text-7xl">
                    Automation that delivers.<br />
                    <span className="bg-gradient-to-r from-lumen-charcoal to-lumen-mid-gray bg-clip-text text-transparent">
                      ROI that's measurable.
                    </span>
                  </h1>
                  <p className="text-xl lg:text-2xl text-lumen-mid-gray leading-relaxed max-w-3xl mx-auto">
                    Custom software and automation that cuts manual work by 50%+ and pays for itself in months, not years.
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-gradient-to-r from-lumen-yellow to-lumen-yellow-hover hover:from-lumen-yellow-hover hover:to-lumen-yellow text-lumen-charcoal font-bold px-10 py-6 text-lg rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
                    Book ROI assessment
                  </Button>
                  <Button variant="outline" size="lg" className="border-2 border-lumen-yellow/30 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg rounded-2xl hover:bg-lumen-yellow/10 transition-all duration-300">
                    Free automation audit
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Services Grid Section - matching homepage style */}
        <section className="w-full bg-gradient-to-br from-lumen-off-white via-white to-lumen-off-white py-20 lg:py-28 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-lumen-yellow/3 via-transparent to-lumen-yellow/5"></div>
          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
                Complete automation solutions
              </h2>
              <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto leading-relaxed">
                From simple task automation to complex custom software, we deliver measurable efficiency gains with bold commitments you can count on.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="group bg-white/80 backdrop-blur-sm rounded-xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-lumen-yellow/40"
                >
                  <div className="text-3xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <service.icon className="h-8 w-8 text-lumen-yellow" />
                  </div>
                  <h3 className="text-xl font-bold text-lumen-charcoal mb-3 group-hover:text-lumen-yellow-hover transition-colors duration-300">
                    {service.title}
                  </h3>
                  <div className="text-sm text-lumen-yellow font-medium mb-3 uppercase tracking-wide">
                    {service.commitment}
                  </div>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    {service.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Capabilities Section */}
        <section className="w-full bg-white py-20 lg:py-28">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
                End-to-end automation expertise
              </h2>
              <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto leading-relaxed">
                We handle everything from process analysis to ongoing support, so you can focus on growing your business while we eliminate the busy work.
              </p>
            </div>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {capabilities.map((capability, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-1" />
                  <span className="text-lumen-charcoal text-lg">{capability}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Automation Examples Section */}
        <section className="w-full bg-gradient-to-br from-lumen-off-white via-white to-lumen-off-white py-16 lg:py-20 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-lumen-yellow/3 via-transparent to-lumen-yellow/5"></div>
          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal section-beam mb-6">
                Automation that delivers results
              </h2>
              <p className="text-lg text-lumen-mid-gray max-w-2xl mx-auto leading-relaxed">
                Real automation solutions we've built for businesses, with measurable ROI and efficiency gains.
              </p>
            </div>

            {/* Grid Container */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {automationExamples.map((item, index) => (
                <div
                  key={index}
                  className="group relative bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
                >
                  {/* Image - 16:9 aspect ratio for 1080p screens */}
                  <div className="relative aspect-[16/9] overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                    />

                    {/* Overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-t from-lumen-charcoal/90 via-lumen-charcoal/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <div className="flex items-center justify-between mb-3">
                          <span className="px-3 py-1 bg-lumen-yellow text-lumen-charcoal text-xs font-medium rounded-full">
                            {item.category}
                          </span>
                        </div>
                        <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                        <p className="text-white/90 text-sm mb-3">{item.description}</p>
                        <div className="space-y-1">
                          {item.metrics.map((metric, metricIndex) => (
                            <div key={metricIndex} className="text-xs text-lumen-yellow font-medium">
                              • {metric}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Card Content */}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="px-3 py-1 bg-lumen-yellow/20 text-lumen-charcoal text-xs font-medium rounded-full">
                        {item.category}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold text-lumen-charcoal mb-2 group-hover:text-lumen-yellow-hover transition-colors duration-300">
                      {item.title}
                    </h3>
                    <p className="text-lumen-mid-gray text-sm mb-4">{item.description}</p>
                    <div className="space-y-2">
                      {item.metrics.map((metric, metricIndex) => (
                        <div key={metricIndex} className="text-xs text-lumen-yellow font-medium">
                          • {metric}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="w-full bg-white py-16 lg:py-20">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal section-beam mb-6">
                What business owners say
              </h2>
              <p className="text-lg text-lumen-mid-gray max-w-2xl mx-auto leading-relaxed">
                Real results from businesses that trusted us to automate their operations and boost efficiency.
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-3 gap-8">
                {testimonials.map((testimonial, index) => (
                  <div key={index} className="text-center">
                    <blockquote className="text-xl lg:text-xl text-lumen-charcoal mb-6 leading-relaxed">
                      "{testimonial.quote}"
                    </blockquote>
                    <div className="space-y-1">
                      <div className="font-bold text-lumen-charcoal">
                        {testimonial.author}
                      </div>
                      <div className="text-lumen-mid-gray">
                        {testimonial.company} • {testimonial.size}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section - matching homepage style */}
        <section className="w-full bg-lumen-yellow py-16 lg:py-20">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-4">
                Ready to automate your business?
              </h2>
              <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto">
                Let's discuss how we can build custom automation that delivers measurable ROI. Professional assessment to identify your highest-impact opportunities.
              </p>
            </div>

            <div className="text-center">
              <Button
                size="lg"
                className="bg-lumen-charcoal hover:bg-lumen-charcoal/90 text-white font-bold py-6 px-12 text-lg rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
              >
                Book your ROI assessment
              </Button>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default InternalSoftware;
